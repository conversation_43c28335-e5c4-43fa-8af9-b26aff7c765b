package repositories

import (
	"context"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AIGenerationRepository struct {
	db *gorm.DB
}

func NewAIGenerationRepository(db *gorm.DB) *AIGenerationRepository {
	return &AIGenerationRepository{db: db}
}

// <PERSON>reate creates a new AI generation job
func (r *AIGenerationRepository) Create(ctx context.Context, job *models.AIGenerationJob) error {
	return r.db.WithContext(ctx).Create(job).Error
}

// GetByID retrieves an AI generation job by ID
func (r *AIGenerationRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.AIGenerationJob, error) {
	var job models.AIGenerationJob
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("User").
		First(&job, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

// GetByBranchID retrieves AI generation jobs for a specific branch with filters
func (r *AIGenerationRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.AIGenerationJobFilters) ([]models.AIGenerationJob, int64, error) {
	var jobs []models.AIGenerationJob
	var total int64

	// Build base query
	query := r.db.WithContext(ctx).Model(&models.AIGenerationJob{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.UserID != nil {
		query = query.Where("user_id = ?", *filters.UserID)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder == "desc" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query with preloads
	err := query.
		Preload("Branch").
		Preload("User").
		Find(&jobs).Error

	return jobs, total, err
}

// Update updates an AI generation job
func (r *AIGenerationRepository) Update(ctx context.Context, job *models.AIGenerationJob) error {
	return r.db.WithContext(ctx).Save(job).Error
}

// UpdateStatus updates the status of an AI generation job
func (r *AIGenerationRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status string, progress int) error {
	return r.db.WithContext(ctx).
		Model(&models.AIGenerationJob{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":   status,
			"progress": progress,
		}).Error
}

// UpdateProgress updates the progress of an AI generation job
func (r *AIGenerationRepository) UpdateProgress(ctx context.Context, id uuid.UUID, progress int) error {
	return r.db.WithContext(ctx).
		Model(&models.AIGenerationJob{}).
		Where("id = ?", id).
		Update("progress", progress).Error
}

// MarkAsStarted marks a job as started
func (r *AIGenerationRepository) MarkAsStarted(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&models.AIGenerationJob{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     "processing",
			"progress":   10,
			"started_at": "NOW()",
		}).Error
}

// MarkAsCompleted marks a job as completed
func (r *AIGenerationRepository) MarkAsCompleted(ctx context.Context, id uuid.UUID, outputData models.AIGenerationOutputData) error {
	return r.db.WithContext(ctx).
		Model(&models.AIGenerationJob{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       "completed",
			"progress":     100,
			"output_data":  outputData,
			"completed_at": "NOW()",
		}).Error
}

// MarkAsFailed marks a job as failed
func (r *AIGenerationRepository) MarkAsFailed(ctx context.Context, id uuid.UUID, errorMsg string) error {
	return r.db.WithContext(ctx).
		Model(&models.AIGenerationJob{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       "failed",
			"error_msg":    errorMsg,
			"completed_at": "NOW()",
		}).Error
}

// GetPendingJobs retrieves all pending AI generation jobs
func (r *AIGenerationRepository) GetPendingJobs(ctx context.Context) ([]models.AIGenerationJob, error) {
	var jobs []models.AIGenerationJob
	err := r.db.WithContext(ctx).
		Where("status = ?", "pending").
		Order("created_at ASC").
		Find(&jobs).Error
	return jobs, err
}

// GetProcessingJobs retrieves all processing AI generation jobs
func (r *AIGenerationRepository) GetProcessingJobs(ctx context.Context) ([]models.AIGenerationJob, error) {
	var jobs []models.AIGenerationJob
	err := r.db.WithContext(ctx).
		Where("status = ?", "processing").
		Order("started_at ASC").
		Find(&jobs).Error
	return jobs, err
}

// Delete deletes an AI generation job
func (r *AIGenerationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.AIGenerationJob{}, "id = ?", id).Error
}

// GetUserJobs retrieves AI generation jobs for a specific user
func (r *AIGenerationRepository) GetUserJobs(ctx context.Context, userID uuid.UUID, filters types.AIGenerationJobFilters) ([]models.AIGenerationJob, int64, error) {
	var jobs []models.AIGenerationJob
	var total int64

	// Build base query
	query := r.db.WithContext(ctx).Model(&models.AIGenerationJob{}).Where("user_id = ?", userID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder == "desc" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query with preloads
	err := query.
		Preload("Branch").
		Preload("User").
		Find(&jobs).Error

	return jobs, total, err
}
