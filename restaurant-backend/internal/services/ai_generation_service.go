package services

import (
	"context"
	"fmt"
	"math/rand/v2"
	"strings"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"
	"restaurant-backend/internal/websocket"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type AIGenerationService struct {
	aiRepo        *repositories.AIGenerationRepository
	menuService   *MenuService
	openaiService *OpenAIService
	hub           *websocket.Hub
	logger        *logrus.Logger
}

func NewAIGenerationService(
	aiRepo *repositories.AIGenerationRepository,
	menuService *MenuService,
	openaiService *OpenAIService,
	hub *websocket.Hub,
	logger *logrus.Logger,
) *AIGenerationService {
	return &AIGenerationService{
		aiRepo:        aiRepo,
		menuService:   menuService,
		openaiService: openaiService,
		hub:           hub,
		logger:        logger,
	}
}

// C<PERSON><PERSON><PERSON> creates a new AI generation job
func (s *AIGenerationService) CreateJob(ctx context.Context, branchID, userID uuid.UUID, req types.CreateAIGenerationJobRequest) (*types.AIGenerationJobResponse, error) {
	// Validate input data based on type
	if err := s.validateInputData(req.Type, req.InputData); err != nil {
		return nil, fmt.Errorf("invalid input data: %w", err)
	}

	// Create job model
	job := &models.AIGenerationJob{
		BranchID: branchID,
		UserID:   userID,
		Type:     req.Type,
		Status:   "pending",
		Progress: 0,
		InputData: models.AIGenerationInputData{
			MenuImageURL:   req.InputData.MenuImageURL,
			MenuText:       req.InputData.MenuText,
			FoodImageURLs:  req.InputData.FoodImageURLs,
			CuisineType:    req.InputData.CuisineType,
			PriceRange:     req.InputData.PriceRange,
			RestaurantName: req.InputData.RestaurantName,
		},
		OutputData: models.AIGenerationOutputData{
			MenuItems: []models.AIGeneratedMenuItem{},
		},
	}

	// Save job to database
	if err := s.aiRepo.Create(ctx, job); err != nil {
		s.logger.WithError(err).Error("Failed to create AI generation job")
		return nil, fmt.Errorf("failed to create job: %w", err)
	}

	// Convert to response
	response := s.convertJobToResponse(*job)

	// Note: Job processing will be handled by the queue system
	// The queue will call ProcessJobAsync when ready
	s.logger.WithField("job_id", job.ID).Info("AI generation job created and ready for queue processing")

	return &response, nil
}

// GetJob retrieves an AI generation job by ID
func (s *AIGenerationService) GetJob(ctx context.Context, id uuid.UUID) (*types.AIGenerationJobResponse, error) {
	job, err := s.aiRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	response := s.convertJobToResponse(*job)
	return &response, nil
}

// GetJobsByBranch retrieves AI generation jobs for a branch
func (s *AIGenerationService) GetJobsByBranch(ctx context.Context, branchID uuid.UUID, filters types.AIGenerationJobFilters) (*types.AIGenerationJobsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	jobs, total, err := s.aiRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.AIGenerationJobResponse, len(jobs))
	for i, job := range jobs {
		jobResponses[i] = s.convertJobToResponse(job)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.AIGenerationJobsResponse{
		Jobs:       jobResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// PublishGeneratedMenu publishes AI generated menu items to the actual menu
func (s *AIGenerationService) PublishGeneratedMenu(ctx context.Context, branchID uuid.UUID, req types.PublishAIGeneratedMenuRequest) error {
	// Get the job to verify it exists and is completed
	job, err := s.aiRepo.GetByID(ctx, req.JobID)
	if err != nil {
		return fmt.Errorf("job not found: %w", err)
	}

	if job.Status != "completed" {
		return fmt.Errorf("job is not completed")
	}

	if job.BranchID != branchID {
		return fmt.Errorf("job does not belong to this branch")
	}

	// Create categories and menu items
	for _, item := range req.MenuItems {
		// Find or create category
		var categoryID *uuid.UUID
		if item.CategoryName != "" {
			category, err := s.findOrCreateCategory(ctx, branchID, item.CategoryName)
			if err != nil {
				s.logger.WithError(err).Warnf("Failed to create category %s", item.CategoryName)
			} else {
				categoryID = &category.ID
			}
		}

		// Create menu item
		menuItemReq := types.CreateMenuItemRequest{
			CategoryID:      categoryID,
			Name:            item.Name,
			Description:     item.Description,
			Price:           item.Price,
			Images:          []string{item.ImageURL},
			Ingredients:     item.Ingredients,
			Allergens:       item.Allergens,
			PreparationTime: &item.PreparationTime,
			IsAvailable:     true,
			IsVegetarian:    item.IsVegetarian,
			IsVegan:         item.IsVegan,
			IsGlutenFree:    item.IsGlutenFree,
			IsSpicy:         item.IsSpicy,
			SpiceLevel:      item.SpiceLevel,
			Tags:            item.Tags,
		}

		_, err := s.menuService.CreateMenuItem(ctx, branchID, menuItemReq)
		if err != nil {
			s.logger.WithError(err).Errorf("Failed to create menu item %s", item.Name)
			return fmt.Errorf("failed to create menu item %s: %w", item.Name, err)
		}
	}

	s.logger.WithField("job_id", req.JobID).Info("Successfully published AI generated menu")
	return nil
}

// validateInputData validates the input data based on generation type
func (s *AIGenerationService) validateInputData(generationType string, data types.AIGenerationInputDataRequest) error {
	switch generationType {
	case "menu_image":
		if data.MenuImageURL == "" {
			return fmt.Errorf("menu_image_url is required for menu_image type")
		}
	case "text":
		if data.MenuText == "" {
			return fmt.Errorf("menu_text is required for text type")
		}
	case "food_images":
		if len(data.FoodImageURLs) == 0 {
			return fmt.Errorf("food_image_urls is required for food_images type")
		}
	default:
		return fmt.Errorf("invalid generation type: %s", generationType)
	}
	return nil
}

// findOrCreateCategory finds an existing category or creates a new one
func (s *AIGenerationService) findOrCreateCategory(ctx context.Context, branchID uuid.UUID, categoryName string) (*models.MenuCategory, error) {
	// Try to find existing category
	filters := types.CategoryFilters{
		Page:   1,
		Limit:  1,
		Search: categoryName,
	}

	categories, err := s.menuService.GetCategories(ctx, branchID, filters)
	if err == nil && len(categories.Data) > 0 {
		// Convert response back to model (this is a bit hacky, but works for now)
		category := &models.MenuCategory{
			BaseModel: models.BaseModel{ID: categories.Data[0].ID},
			BranchID:  branchID,
			Name:      categories.Data[0].Name,
			Slug:      categories.Data[0].Slug,
		}
		return category, nil
	}

	// Create new category
	createReq := types.CreateCategoryRequest{
		Name:        categoryName,
		Description: fmt.Sprintf("AI generated category for %s", categoryName),
		IsActive:    true,
		SortOrder:   0,
	}

	categoryResp, err := s.menuService.CreateCategory(ctx, branchID, createReq)
	if err != nil {
		return nil, err
	}

	category := &models.MenuCategory{
		BaseModel: models.BaseModel{ID: categoryResp.ID},
		BranchID:  branchID,
		Name:      categoryResp.Name,
		Slug:      categoryResp.Slug,
	}

	return category, nil
}

// ProcessJobAsync processes an AI generation job asynchronously (public method for queue)
func (s *AIGenerationService) ProcessJobAsync(ctx context.Context, jobID uuid.UUID) error {
	return s.processJobAsync(ctx, jobID)
}

// processJobAsync processes an AI generation job asynchronously
func (s *AIGenerationService) processJobAsync(ctx context.Context, jobID uuid.UUID) error {
	// Get the job
	job, err := s.aiRepo.GetByID(ctx, jobID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get job for processing")
		return err
	}

	// Mark job as started
	if err := s.aiRepo.MarkAsStarted(ctx, jobID); err != nil {
		s.logger.WithError(err).Error("Failed to mark job as started")
		return err
	}

	// Send WebSocket update
	s.sendStatusUpdate(job.BranchID, jobID, "processing", 10, "Starting AI generation...")

	// Process based on type
	var outputData models.AIGenerationOutputData
	switch job.Type {
	case "menu_image":
		outputData, err = s.processMenuImage(ctx, job)
	case "text":
		outputData, err = s.processMenuText(ctx, job)
	case "food_images":
		outputData, err = s.processFoodImages(ctx, job)
	default:
		err = fmt.Errorf("unsupported generation type: %s", job.Type)
	}

	if err != nil {
		// Mark job as failed
		s.aiRepo.MarkAsFailed(ctx, jobID, err.Error())
		s.sendStatusUpdate(job.BranchID, jobID, "failed", 0, "", err.Error())
		s.logger.WithError(err).Error("Failed to process AI generation job")
		return err
	}

	// Mark job as completed
	if err := s.aiRepo.MarkAsCompleted(ctx, jobID, outputData); err != nil {
		s.logger.WithError(err).Error("Failed to mark job as completed")
		return err
	}

	// Send completion update
	s.sendStatusUpdate(job.BranchID, jobID, "completed", 100, "AI generation completed successfully!")
	s.logger.WithField("job_id", jobID).Info("AI generation job completed successfully")
	return nil
}

// processMenuImage processes a menu image and generates menu items
func (s *AIGenerationService) processMenuImage(ctx context.Context, job *models.AIGenerationJob) (models.AIGenerationOutputData, error) {
	s.logger.WithField("job_id", job.ID).Info("Processing menu image with OpenAI")

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 30)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 30, "Analyzing menu image with AI...")

	// Call OpenAI Vision API
	menuItems, err := s.openaiService.AnalyzeMenuImage(
		ctx,
		job.InputData.MenuImageURL,
		job.InputData.CuisineType,
		job.InputData.PriceRange,
		job.InputData.RestaurantName,
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to analyze menu image with OpenAI")
		return models.AIGenerationOutputData{}, fmt.Errorf("failed to analyze menu image: %w", err)
	}

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 90)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 90, "Finalizing menu items...")

	return models.AIGenerationOutputData{
		MenuItems: menuItems,
		MenuInfo: &models.AIGeneratedMenuInfo{
			Name:        "AI Generated Menu",
			Description: "Menu generated from uploaded image using OpenAI Vision",
			ImageURL:    job.InputData.MenuImageURL,
		},
	}, nil
}

// processMenuText processes menu text and generates structured menu items
func (s *AIGenerationService) processMenuText(ctx context.Context, job *models.AIGenerationJob) (models.AIGenerationOutputData, error) {
	s.logger.WithField("job_id", job.ID).Info("Processing menu text with OpenAI")

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 40)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 40, "Enhancing menu text with AI...")

	// Call OpenAI to enhance menu text
	menuItems, err := s.openaiService.EnhanceMenuText(
		ctx,
		job.InputData.MenuText,
		job.InputData.CuisineType,
		job.InputData.PriceRange,
		job.InputData.RestaurantName,
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to enhance menu text with OpenAI")
		return models.AIGenerationOutputData{}, fmt.Errorf("failed to enhance menu text: %w", err)
	}

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 90)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 90, "Finalizing menu items...")

	return models.AIGenerationOutputData{
		MenuItems: menuItems,
		MenuInfo: &models.AIGeneratedMenuInfo{
			Name:        "Text-Based Menu",
			Description: "Menu generated from text input using OpenAI",
		},
	}, nil
}

// processFoodImages processes food images and generates menu items
func (s *AIGenerationService) processFoodImages(ctx context.Context, job *models.AIGenerationJob) (models.AIGenerationOutputData, error) {
	s.logger.WithField("job_id", job.ID).Info("Processing food images with OpenAI")

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 25)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 25, "Analyzing food images with AI...")

	// Call OpenAI Vision API for food images
	menuItems, err := s.openaiService.AnalyzeFoodImages(
		ctx,
		job.InputData.FoodImageURLs,
		job.InputData.CuisineType,
		job.InputData.PriceRange,
		job.InputData.RestaurantName,
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to analyze food images with OpenAI")
		return models.AIGenerationOutputData{}, fmt.Errorf("failed to analyze food images: %w", err)
	}

	// Update progress
	s.aiRepo.UpdateProgress(ctx, job.ID, 90)
	s.sendStatusUpdate(job.BranchID, job.ID, "processing", 90, "Finalizing menu items...")

	return models.AIGenerationOutputData{
		MenuItems: menuItems,
		MenuInfo: &models.AIGeneratedMenuInfo{
			Name:        "Image-Based Menu",
			Description: "Menu generated from food images using OpenAI Vision",
		},
	}, nil
}

// Helper methods

// convertJobToResponse converts a job model to response format
func (s *AIGenerationService) convertJobToResponse(job models.AIGenerationJob) types.AIGenerationJobResponse {
	response := types.AIGenerationJobResponse{
		ID:         job.ID,
		BranchID:   job.BranchID,
		UserID:     job.UserID,
		Type:       job.Type,
		Status:     job.Status,
		Progress:   job.Progress,
		InputData:  job.InputData,
		OutputData: job.OutputData,
		ErrorMsg:   job.ErrorMsg,
		CreatedAt:  job.CreatedAt.Format(time.RFC3339),
		UpdatedAt:  job.UpdatedAt.Format(time.RFC3339),
	}

	if job.StartedAt != nil {
		startedAt := job.StartedAt.Format(time.RFC3339)
		response.StartedAt = &startedAt
	}

	if job.CompletedAt != nil {
		completedAt := job.CompletedAt.Format(time.RFC3339)
		response.CompletedAt = &completedAt
	}

	return response
}

// sendStatusUpdate sends a WebSocket status update
func (s *AIGenerationService) sendStatusUpdate(branchID, jobID uuid.UUID, status string, progress int, message string, errorMsg ...string) {
	if s.hub == nil {
		return
	}

	update := types.AIGenerationStatusUpdate{
		JobID:    jobID,
		Status:   status,
		Progress: progress,
		Message:  message,
	}

	if len(errorMsg) > 0 {
		update.Error = errorMsg[0]
	}

	s.hub.BroadcastNotification(&branchID, nil, update)
}

// generateMockMenuItems generates mock menu items for testing
func (s *AIGenerationService) generateMockMenuItems(cuisineType, priceRange string) []models.AIGeneratedMenuItem {
	// Mock menu items based on cuisine type
	var baseItems []models.AIGeneratedMenuItem

	switch strings.ToLower(cuisineType) {
	case "italian":
		baseItems = []models.AIGeneratedMenuItem{
			{
				Name:            "Spaghetti Carbonara",
				Description:     "Classic pasta dish with creamy sauce, pancetta, and Parmesan cheese",
				Price:           18.99,
				Category:        "Main Course",
				ImageURL:        "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400",
				Ingredients:     []string{"spaghetti", "eggs", "pancetta", "parmesan", "black pepper"},
				IsVegetarian:    false,
				IsVegan:         false,
				IsGlutenFree:    false,
				IsSpicy:         false,
				SpiceLevel:      0,
				PreparationTime: 15,
				Tags:            []string{"pasta", "classic", "creamy"},
			},
			{
				Name:            "Margherita Pizza",
				Description:     "Traditional pizza with tomato sauce, mozzarella, and fresh basil",
				Price:           15.99,
				Category:        "Main Course",
				ImageURL:        "https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400",
				Ingredients:     []string{"pizza dough", "tomato sauce", "mozzarella", "basil"},
				IsVegetarian:    true,
				IsVegan:         false,
				IsGlutenFree:    false,
				IsSpicy:         false,
				SpiceLevel:      0,
				PreparationTime: 12,
				Tags:            []string{"pizza", "vegetarian", "classic"},
			},
		}
	case "thai":
		baseItems = []models.AIGeneratedMenuItem{
			{
				Name:            "Pad Thai",
				Description:     "Stir-fried rice noodles with shrimp, tofu, bean sprouts, and tamarind sauce",
				Price:           14.99,
				Category:        "Main Course",
				ImageURL:        "https://images.unsplash.com/photo-1559314809-0f31657def5e?w=400",
				Ingredients:     []string{"rice noodles", "shrimp", "tofu", "bean sprouts", "tamarind"},
				IsVegetarian:    false,
				IsVegan:         false,
				IsGlutenFree:    true,
				IsSpicy:         true,
				SpiceLevel:      2,
				PreparationTime: 10,
				Tags:            []string{"noodles", "spicy", "traditional"},
			},
			{
				Name:            "Green Curry",
				Description:     "Aromatic curry with coconut milk, Thai basil, and your choice of protein",
				Price:           16.99,
				Category:        "Main Course",
				ImageURL:        "https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=400",
				Ingredients:     []string{"green curry paste", "coconut milk", "thai basil", "chicken"},
				IsVegetarian:    false,
				IsVegan:         false,
				IsGlutenFree:    true,
				IsSpicy:         true,
				SpiceLevel:      3,
				PreparationTime: 20,
				Tags:            []string{"curry", "spicy", "coconut"},
			},
		}
	default:
		baseItems = []models.AIGeneratedMenuItem{
			{
				Name:            "Grilled Chicken Breast",
				Description:     "Tender grilled chicken breast with herbs and seasonal vegetables",
				Price:           19.99,
				Category:        "Main Course",
				ImageURL:        "https://images.unsplash.com/photo-1532550907401-a500c9a57435?w=400",
				Ingredients:     []string{"chicken breast", "herbs", "vegetables"},
				IsVegetarian:    false,
				IsVegan:         false,
				IsGlutenFree:    true,
				IsSpicy:         false,
				SpiceLevel:      0,
				PreparationTime: 25,
				Tags:            []string{"grilled", "healthy", "protein"},
			},
			{
				Name:            "Caesar Salad",
				Description:     "Fresh romaine lettuce with Caesar dressing, croutons, and parmesan",
				Price:           12.99,
				Category:        "Appetizer",
				ImageURL:        "https://images.unsplash.com/photo-**********-c74683f339c1?w=400",
				Ingredients:     []string{"romaine lettuce", "caesar dressing", "croutons", "parmesan"},
				IsVegetarian:    true,
				IsVegan:         false,
				IsGlutenFree:    false,
				IsSpicy:         false,
				SpiceLevel:      0,
				PreparationTime: 5,
				Tags:            []string{"salad", "fresh", "vegetarian"},
			},
		}
	}

	// Adjust prices based on price range
	multiplier := 1.0
	switch strings.ToLower(priceRange) {
	case "$":
		multiplier = 0.7
	case "$$":
		multiplier = 1.0
	case "$$$":
		multiplier = 1.5
	case "$$$$":
		multiplier = 2.0
	}

	for i := range baseItems {
		baseItems[i].Price = baseItems[i].Price * multiplier
	}

	return baseItems
}

// parseMenuTextToItems parses menu text and generates menu items
func (s *AIGenerationService) parseMenuTextToItems(menuText, cuisineType string) []models.AIGeneratedMenuItem {
	// Simple text parsing (in real implementation, this would use AI)
	lines := strings.Split(menuText, "\n")
	var items []models.AIGeneratedMenuItem

	currentCategory := "Main Course"

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Check if line is a category header (all caps or ends with colon)
		if strings.ToUpper(line) == line && len(line) > 3 {
			currentCategory = strings.Title(strings.ToLower(line))
			continue
		}

		// Try to parse item with price
		parts := strings.Split(line, " - ")
		if len(parts) >= 2 {
			name := strings.TrimSpace(parts[0])
			priceStr := strings.TrimSpace(parts[1])

			// Extract price
			price := 15.99 // default price
			if strings.Contains(priceStr, "$") {
				priceStr = strings.ReplaceAll(priceStr, "$", "")
				// Generate random price between 10-30
				price = rand.Float64()*20 + 10
			}

			item := models.AIGeneratedMenuItem{
				Name:            name,
				Description:     fmt.Sprintf("Delicious %s prepared with care", name),
				Price:           price,
				Category:        currentCategory,
				ImageURL:        "https://images.unsplash.com/photo-**********-c74683f339c1?w=400",
				Ingredients:     []string{"fresh ingredients"},
				IsVegetarian:    strings.Contains(strings.ToLower(name), "vegetarian") || strings.Contains(strings.ToLower(name), "salad"),
				IsVegan:         strings.Contains(strings.ToLower(name), "vegan"),
				IsGlutenFree:    strings.Contains(strings.ToLower(name), "gluten-free"),
				IsSpicy:         strings.Contains(strings.ToLower(name), "spicy") || strings.Contains(strings.ToLower(cuisineType), "thai"),
				SpiceLevel:      0,
				PreparationTime: 15,
				Tags:            []string{"fresh", "homemade"},
			}

			if item.IsSpicy {
				item.SpiceLevel = 2
			}

			items = append(items, item)
		}
	}

	// If no items parsed, return some default items
	if len(items) == 0 {
		return s.generateMockMenuItems(cuisineType, "$$")
	}

	return items
}

// generateMenuItemsFromImages generates menu items from food images
func (s *AIGenerationService) generateMenuItemsFromImages(imageURLs []string, cuisineType, priceRange string) []models.AIGeneratedMenuItem {
	var items []models.AIGeneratedMenuItem

	// Generate items based on number of images
	dishNames := []string{
		"Signature Dish", "Chef's Special", "House Favorite", "Daily Special",
		"Seasonal Delight", "Traditional Recipe", "Modern Fusion", "Classic Comfort",
	}

	for i, imageURL := range imageURLs {
		if i >= len(dishNames) {
			break
		}

		// Base price varies by image position
		basePrice := 15.0 + float64(i)*3.0

		// Adjust by price range
		multiplier := 1.0
		switch strings.ToLower(priceRange) {
		case "$":
			multiplier = 0.7
		case "$$":
			multiplier = 1.0
		case "$$$":
			multiplier = 1.5
		case "$$$$":
			multiplier = 2.0
		}

		item := models.AIGeneratedMenuItem{
			Name:            dishNames[i],
			Description:     fmt.Sprintf("Expertly crafted %s with premium ingredients", dishNames[i]),
			Price:           basePrice * multiplier,
			Category:        "Main Course",
			ImageURL:        imageURL,
			Ingredients:     []string{"premium ingredients", "fresh herbs", "seasonal vegetables"},
			IsVegetarian:    i%3 == 0, // Every third item is vegetarian
			IsVegan:         i%5 == 0, // Every fifth item is vegan
			IsGlutenFree:    i%4 == 0, // Every fourth item is gluten-free
			IsSpicy:         strings.Contains(strings.ToLower(cuisineType), "thai") || strings.Contains(strings.ToLower(cuisineType), "indian"),
			SpiceLevel:      0,
			PreparationTime: 20 + i*5,
			Tags:            []string{"signature", "premium", "chef-recommended"},
		}

		if item.IsSpicy {
			item.SpiceLevel = 2 + i%3 // Spice level 2-4
		}

		items = append(items, item)
	}

	return items
}
