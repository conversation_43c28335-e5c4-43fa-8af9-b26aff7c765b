package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/models"

	"github.com/sashabaranov/go-openai"
	"github.com/sirupsen/logrus"
)

type OpenAIService struct {
	client *openai.Client
	config *config.OpenAIConfig
	logger *logrus.Logger
}

func NewOpenAIService(cfg *config.OpenAIConfig, logger *logrus.Logger) *OpenAIService {
	client := openai.NewClient(cfg.APIKey)
	return &OpenAIService{
		client: client,
		config: cfg,
		logger: logger,
	}
}

// AnalyzeMenuImage analyzes a menu image and extracts menu items
func (s *OpenAIService) AnalyzeMenuImage(ctx context.Context, imageURL, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	prompt := s.buildMenuImagePrompt(cuisineType, priceRange, restaurantName)

	resp, err := s.client.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model:       s.config.Model,
			MaxTokens:   s.config.MaxTokens,
			Temperature: s.config.Temperature,
			Messages: []openai.ChatCompletionMessage{
				{
					Role: openai.ChatMessageRoleUser,
					MultiContent: []openai.ChatMessagePart{
						{
							Type: openai.ChatMessagePartTypeText,
							Text: prompt,
						},
						{
							Type: openai.ChatMessagePartTypeImageURL,
							ImageURL: &openai.ChatMessageImageURL{
								URL: imageURL,
							},
						},
					},
				},
			},
		},
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to analyze menu image with OpenAI")
		return nil, fmt.Errorf("failed to analyze menu image: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI")
	}

	content := resp.Choices[0].Message.Content
	menuItems, err := s.parseMenuItemsFromJSON(content)
	if err != nil {
		s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
		return nil, fmt.Errorf("failed to parse menu items: %w", err)
	}

	return menuItems, nil
}

// AnalyzeFoodImages analyzes food images and generates menu items
func (s *OpenAIService) AnalyzeFoodImages(ctx context.Context, imageURLs []string, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	var allMenuItems []models.AIGeneratedMenuItem

	for i, imageURL := range imageURLs {
		prompt := s.buildFoodImagePrompt(cuisineType, priceRange, restaurantName, i+1)

		resp, err := s.client.CreateChatCompletion(
			ctx,
			openai.ChatCompletionRequest{
				Model:       s.config.Model,
				MaxTokens:   s.config.MaxTokens,
				Temperature: s.config.Temperature,
				Messages: []openai.ChatCompletionMessage{
					{
						Role: openai.ChatMessageRoleUser,
						MultiContent: []openai.ChatMessagePart{
							{
								Type: openai.ChatMessagePartTypeText,
								Text: prompt,
							},
							{
								Type: openai.ChatMessagePartTypeImageURL,
								ImageURL: &openai.ChatMessageImageURL{
									URL: imageURL,
								},
							},
						},
					},
				},
			},
		)
		if err != nil {
			s.logger.WithError(err).WithField("image_url", imageURL).Error("Failed to analyze food image with OpenAI")
			continue // Skip this image and continue with others
		}

		if len(resp.Choices) == 0 {
			s.logger.WithField("image_url", imageURL).Warn("No response from OpenAI for food image")
			continue
		}

		content := resp.Choices[0].Message.Content
		menuItems, err := s.parseMenuItemsFromJSON(content)
		if err != nil {
			s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
			continue
		}

		// Set the image URL for the generated items
		for j := range menuItems {
			menuItems[j].ImageURL = imageURL
		}

		allMenuItems = append(allMenuItems, menuItems...)
	}

	if len(allMenuItems) == 0 {
		return nil, fmt.Errorf("failed to generate any menu items from food images")
	}

	return allMenuItems, nil
}

// EnhanceMenuText processes menu text and generates structured menu items
func (s *OpenAIService) EnhanceMenuText(ctx context.Context, menuText, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	prompt := s.buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName)

	resp, err := s.client.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model:       "gpt-4", // Use regular GPT-4 for text processing
			MaxTokens:   s.config.MaxTokens,
			Temperature: s.config.Temperature,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: prompt,
				},
			},
		},
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to enhance menu text with OpenAI")
		return nil, fmt.Errorf("failed to enhance menu text: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI")
	}

	content := resp.Choices[0].Message.Content
	menuItems, err := s.parseMenuItemsFromJSON(content)
	if err != nil {
		s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
		return nil, fmt.Errorf("failed to parse menu items: %w", err)
	}

	return menuItems, nil
}

// buildMenuImagePrompt creates a prompt for menu image analysis
func (s *OpenAIService) buildMenuImagePrompt(cuisineType, priceRange, restaurantName string) string {
	return fmt.Sprintf(`Analyze this menu image and extract all menu items. Return the result as a JSON array of menu items.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s

For each menu item, provide:
- name: The dish name
- description: A detailed, appetizing description (2-3 sentences)
- price: Estimated price as a number (consider the price range)
- category: Food category (e.g., "Appetizers", "Main Course", "Desserts")
- ingredients: Array of main ingredients
- allergens: Array of common allergens if applicable
- is_vegetarian: boolean
- is_vegan: boolean
- is_gluten_free: boolean
- is_spicy: boolean
- spice_level: number 0-5 (0 = not spicy)
- preparation_time: estimated minutes
- tags: Array of relevant tags

Return ONLY a valid JSON array, no additional text or formatting.`, restaurantName, cuisineType, priceRange)
}

// buildFoodImagePrompt creates a prompt for food image analysis
func (s *OpenAIService) buildFoodImagePrompt(cuisineType, priceRange, restaurantName string, imageNumber int) string {
	return fmt.Sprintf(`Analyze this food image and create a menu item for it. Return the result as a JSON array with one menu item.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s
- Image #%d

Create an appealing menu item based on what you see in the image. Include:
- name: Creative, appetizing dish name
- description: Detailed, mouth-watering description (2-3 sentences)
- price: Appropriate price as a number (consider the price range)
- category: Food category based on the dish type
- ingredients: Array of visible/likely ingredients
- allergens: Array of potential allergens
- is_vegetarian: boolean based on visible ingredients
- is_vegan: boolean based on visible ingredients
- is_gluten_free: boolean assessment
- is_spicy: boolean based on visual cues
- spice_level: number 0-5 if spicy
- preparation_time: estimated cooking time in minutes
- tags: Array of descriptive tags

Return ONLY a valid JSON array with one object, no additional text.`, restaurantName, cuisineType, priceRange, imageNumber)
}

// buildTextEnhancementPrompt creates a prompt for text enhancement
func (s *OpenAIService) buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName string) string {
	return fmt.Sprintf(`Parse and enhance this menu text into structured menu items. Return as a JSON array.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s

Menu Text:
%s

For each item found in the text, create a structured menu item with:
- name: Clean dish name
- description: Enhanced, appetizing description (2-3 sentences)
- price: Extract or estimate price as number (use price range as guide)
- category: Determine appropriate category
- ingredients: Infer likely ingredients
- allergens: Common allergens for this type of dish
- is_vegetarian: boolean assessment
- is_vegan: boolean assessment
- is_gluten_free: boolean assessment
- is_spicy: boolean based on dish name/description
- spice_level: number 0-5 if spicy
- preparation_time: estimated minutes
- tags: Relevant descriptive tags

Return ONLY a valid JSON array, no additional text.`, restaurantName, cuisineType, priceRange, menuText)
}

// GenerateFoodImage generates a custom food image using DALL-E
func (s *OpenAIService) GenerateFoodImage(ctx context.Context, dishName, description, cuisineType string) (string, error) {
	// Build a detailed prompt for DALL-E
	prompt := s.buildImageGenerationPrompt(dishName, description, cuisineType)

	// Create image request
	req := openai.ImageRequest{
		Prompt:         prompt,
		Model:          openai.CreateImageModelDallE3, // Use DALL-E 3 for best quality
		N:              1,
		Size:           openai.CreateImageSize1024x1024,
		Quality:        openai.CreateImageQualityStandard,
		ResponseFormat: openai.CreateImageResponseFormatURL,
		Style:          openai.CreateImageStyleNatural,
	}

	resp, err := s.client.CreateImage(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"dish_name": dishName,
			"prompt":    prompt,
		}).Error("Failed to generate food image with DALL-E")
		return "", fmt.Errorf("failed to generate food image: %w", err)
	}

	if len(resp.Data) == 0 {
		return "", fmt.Errorf("no image generated by DALL-E")
	}

	imageURL := resp.Data[0].URL
	s.logger.WithFields(logrus.Fields{
		"dish_name": dishName,
		"image_url": imageURL,
		"prompt":    prompt,
	}).Info("Successfully generated food image with DALL-E")

	return imageURL, nil
}

// buildImageGenerationPrompt creates an optimized prompt for DALL-E food image generation
func (s *OpenAIService) buildImageGenerationPrompt(dishName, description, cuisineType string) string {
	// Handle Thai text and enhance cultural authenticity
	enhancedDishName := s.enhanceThaiDishName(dishName)
	enhancedCuisineType := s.enhanceCuisineType(cuisineType, dishName)

	// Create a detailed, photography-style prompt for realistic food images
	basePrompt := fmt.Sprintf("Professional food photography of %s, authentic %s", enhancedDishName, enhancedCuisineType)

	// Add description context if available
	if description != "" {
		// Extract key visual elements from description
		visualElements := s.extractVisualElements(description)
		if visualElements != "" {
			basePrompt += fmt.Sprintf(", %s", visualElements)
		}
	}

	// Add cuisine-specific styling
	culturalElements := s.getCulturalElements(cuisineType, dishName)
	if culturalElements != "" {
		basePrompt += fmt.Sprintf(", %s", culturalElements)
	}

	// Add professional photography specifications
	basePrompt += ", beautifully plated on elegant dishware, natural lighting, shallow depth of field, appetizing presentation, high-resolution, restaurant quality, food styling, warm color palette"

	// Ensure prompt is within DALL-E limits (1000 characters max)
	if len(basePrompt) > 1000 {
		basePrompt = basePrompt[:997] + "..."
	}

	return basePrompt
}

// enhanceThaiDishName enhances Thai dish names for better image generation
func (s *OpenAIService) enhanceThaiDishName(dishName string) string {
	// Common Thai dish translations and enhancements
	thaiDishes := map[string]string{
		"ผัดไทย":           "Pad Thai (traditional Thai stir-fried rice noodles)",
		"ต้มยำกุ้ง":        "Tom Yum Goong (spicy Thai shrimp soup)",
		"แกงเขียวหวาน":     "Green Curry (Thai green curry)",
		"ข้าวเหนียวมะม่วง": "Mango Sticky Rice (Thai dessert)",
		"ส้มตำ":            "Som Tam (Thai papaya salad)",
		"แกงมัสมั่น":       "Massaman Curry (Thai curry)",
		"ผัดกะเพรา":        "Pad Krapow (Thai basil stir-fry)",
		"ต้มข่าไก่":        "Tom Kha Gai (Thai coconut chicken soup)",
		"ลาบ":              "Larb (Thai meat salad)",
		"แกงส้ม":           "Gaeng Som (Thai sour curry)",
	}

	// Check if it's a known Thai dish
	if enhanced, exists := thaiDishes[dishName]; exists {
		return enhanced
	}

	// If it contains Thai characters, add context
	if containsThaiCharacters(dishName) {
		return fmt.Sprintf("%s (authentic Thai dish)", dishName)
	}

	return dishName
}

// enhanceCuisineType enhances cuisine type based on dish name
func (s *OpenAIService) enhanceCuisineType(cuisineType, dishName string) string {
	// Auto-detect Thai cuisine from dish names
	if containsThaiCharacters(dishName) || isThaiDish(dishName) {
		return "Thai cuisine style, traditional Thai cooking methods"
	}

	// Enhance other cuisine types
	switch strings.ToLower(cuisineType) {
	case "thai":
		return "Thai cuisine style, traditional Thai cooking methods"
	case "japanese":
		return "Japanese cuisine style, traditional Japanese presentation"
	case "italian":
		return "Italian cuisine style, traditional Italian cooking"
	case "chinese":
		return "Chinese cuisine style, traditional Chinese cooking methods"
	case "indian":
		return "Indian cuisine style, traditional Indian spices and cooking"
	default:
		if cuisineType != "" {
			return fmt.Sprintf("%s cuisine style", cuisineType)
		}
		return "international cuisine style"
	}
}

// getCulturalElements adds culture-specific visual elements
func (s *OpenAIService) getCulturalElements(cuisineType, dishName string) string {
	// Auto-detect from dish name
	if containsThaiCharacters(dishName) || isThaiDish(dishName) {
		cuisineType = "thai"
	}

	switch strings.ToLower(cuisineType) {
	case "thai":
		return "served on traditional Thai ceramics, garnished with fresh herbs, lime wedges, and chili, authentic Thai presentation"
	case "japanese":
		return "served on minimalist Japanese dishware, clean presentation, traditional Japanese garnish"
	case "italian":
		return "served on rustic Italian dishware, fresh herbs, traditional Italian presentation"
	case "chinese":
		return "served on traditional Chinese porcelain, elegant Chinese presentation style"
	case "indian":
		return "served on traditional Indian dishware, colorful spices, authentic Indian garnish"
	default:
		return ""
	}
}

// containsThaiCharacters checks if string contains Thai Unicode characters
func containsThaiCharacters(text string) bool {
	for _, r := range text {
		if r >= 0x0E00 && r <= 0x0E7F { // Thai Unicode block
			return true
		}
	}
	return false
}

// isThaiDish checks if dish name is a known Thai dish (English names)
func isThaiDish(dishName string) bool {
	thaiDishes := []string{
		"pad thai", "tom yum", "green curry", "massaman", "som tam",
		"pad krapow", "tom kha", "larb", "mango sticky rice",
		"thai curry", "pad see ew", "drunken noodles", "thai fried rice",
	}

	dishLower := strings.ToLower(dishName)
	for _, thai := range thaiDishes {
		if strings.Contains(dishLower, thai) {
			return true
		}
	}
	return false
}

// extractVisualElements extracts visual keywords from description for better image generation
func (s *OpenAIService) extractVisualElements(description string) string {
	description = strings.ToLower(description)
	var elements []string

	// Color keywords
	colorKeywords := []string{"golden", "crispy", "creamy", "rich", "fresh", "vibrant", "caramelized", "grilled", "roasted", "seared"}
	for _, keyword := range colorKeywords {
		if strings.Contains(description, keyword) {
			elements = append(elements, keyword)
		}
	}

	// Texture keywords
	textureKeywords := []string{"crispy", "tender", "juicy", "flaky", "smooth", "chunky", "layered"}
	for _, keyword := range textureKeywords {
		if strings.Contains(description, keyword) && !contains(elements, keyword) {
			elements = append(elements, keyword)
		}
	}

	// Cooking method keywords
	cookingKeywords := []string{"grilled", "roasted", "seared", "braised", "steamed", "fried", "baked"}
	for _, keyword := range cookingKeywords {
		if strings.Contains(description, keyword) && !contains(elements, keyword) {
			elements = append(elements, keyword)
		}
	}

	// Limit to 3-4 most relevant elements
	if len(elements) > 4 {
		elements = elements[:4]
	}

	return strings.Join(elements, ", ")
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// parseMenuItemsFromJSON parses the OpenAI response into menu items
func (s *OpenAIService) parseMenuItemsFromJSON(content string) ([]models.AIGeneratedMenuItem, error) {
	// Clean the content - remove any markdown formatting
	content = strings.TrimSpace(content)
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")
	content = strings.TrimSpace(content)

	var menuItems []models.AIGeneratedMenuItem
	if err := json.Unmarshal([]byte(content), &menuItems); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	// Validate and set defaults for required fields
	for i := range menuItems {
		if menuItems[i].Name == "" {
			menuItems[i].Name = fmt.Sprintf("Menu Item %d", i+1)
		}
		if menuItems[i].Description == "" {
			menuItems[i].Description = "Delicious dish prepared with care"
		}
		if menuItems[i].Price <= 0 {
			menuItems[i].Price = 12.99 // Default price
		}
		if menuItems[i].Category == "" {
			menuItems[i].Category = "Main Course"
		}
		if menuItems[i].PreparationTime <= 0 {
			menuItems[i].PreparationTime = 15 // Default prep time
		}
		if len(menuItems[i].Ingredients) == 0 {
			menuItems[i].Ingredients = []string{"fresh ingredients"}
		}
		if len(menuItems[i].Tags) == 0 {
			menuItems[i].Tags = []string{"fresh", "homemade"}
		}
	}

	return menuItems, nil
}
