package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/models"

	"github.com/sashabaranov/go-openai"
	"github.com/sirupsen/logrus"
)

type OpenAIService struct {
	client *openai.Client
	config *config.OpenAIConfig
	logger *logrus.Logger
}

func NewOpenAIService(cfg *config.OpenAIConfig, logger *logrus.Logger) *OpenAIService {
	client := openai.NewClient(cfg.APIKey)
	return &OpenAIService{
		client: client,
		config: cfg,
		logger: logger,
	}
}

// AnalyzeMenuImage analyzes a menu image and extracts menu items
func (s *OpenAIService) AnalyzeMenuImage(ctx context.Context, imageURL, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	prompt := s.buildMenuImagePrompt(cuisineType, priceRange, restaurantName)

	resp, err := s.client.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model:     s.config.Model,
			MaxTokens: s.config.MaxTokens,
			Temperature: s.config.Temperature,
			Messages: []openai.ChatCompletionMessage{
				{
					Role: openai.ChatMessageRoleUser,
					MultiContent: []openai.ChatMessagePart{
						{
							Type: openai.ChatMessagePartTypeText,
							Text: prompt,
						},
						{
							Type: openai.ChatMessagePartTypeImageURL,
							ImageURL: &openai.ChatMessageImageURL{
								URL: imageURL,
							},
						},
					},
				},
			},
		},
	)

	if err != nil {
		s.logger.WithError(err).Error("Failed to analyze menu image with OpenAI")
		return nil, fmt.Errorf("failed to analyze menu image: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI")
	}

	content := resp.Choices[0].Message.Content
	menuItems, err := s.parseMenuItemsFromJSON(content)
	if err != nil {
		s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
		return nil, fmt.Errorf("failed to parse menu items: %w", err)
	}

	return menuItems, nil
}

// AnalyzeFoodImages analyzes food images and generates menu items
func (s *OpenAIService) AnalyzeFoodImages(ctx context.Context, imageURLs []string, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	var allMenuItems []models.AIGeneratedMenuItem

	for i, imageURL := range imageURLs {
		prompt := s.buildFoodImagePrompt(cuisineType, priceRange, restaurantName, i+1)

		resp, err := s.client.CreateChatCompletion(
			ctx,
			openai.ChatCompletionRequest{
				Model:     s.config.Model,
				MaxTokens: s.config.MaxTokens,
				Temperature: s.config.Temperature,
				Messages: []openai.ChatCompletionMessage{
					{
						Role: openai.ChatMessageRoleUser,
						MultiContent: []openai.ChatMessagePart{
							{
								Type: openai.ChatMessagePartTypeText,
								Text: prompt,
							},
							{
								Type: openai.ChatMessagePartTypeImageURL,
								ImageURL: &openai.ChatMessageImageURL{
									URL: imageURL,
								},
							},
						},
					},
				},
			},
		)

		if err != nil {
			s.logger.WithError(err).WithField("image_url", imageURL).Error("Failed to analyze food image with OpenAI")
			continue // Skip this image and continue with others
		}

		if len(resp.Choices) == 0 {
			s.logger.WithField("image_url", imageURL).Warn("No response from OpenAI for food image")
			continue
		}

		content := resp.Choices[0].Message.Content
		menuItems, err := s.parseMenuItemsFromJSON(content)
		if err != nil {
			s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
			continue
		}

		// Set the image URL for the generated items
		for j := range menuItems {
			menuItems[j].ImageURL = imageURL
		}

		allMenuItems = append(allMenuItems, menuItems...)
	}

	if len(allMenuItems) == 0 {
		return nil, fmt.Errorf("failed to generate any menu items from food images")
	}

	return allMenuItems, nil
}

// EnhanceMenuText processes menu text and generates structured menu items
func (s *OpenAIService) EnhanceMenuText(ctx context.Context, menuText, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	prompt := s.buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName)

	resp, err := s.client.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model:       "gpt-4", // Use regular GPT-4 for text processing
			MaxTokens:   s.config.MaxTokens,
			Temperature: s.config.Temperature,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: prompt,
				},
			},
		},
	)

	if err != nil {
		s.logger.WithError(err).Error("Failed to enhance menu text with OpenAI")
		return nil, fmt.Errorf("failed to enhance menu text: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI")
	}

	content := resp.Choices[0].Message.Content
	menuItems, err := s.parseMenuItemsFromJSON(content)
	if err != nil {
		s.logger.WithError(err).WithField("content", content).Error("Failed to parse menu items from OpenAI response")
		return nil, fmt.Errorf("failed to parse menu items: %w", err)
	}

	return menuItems, nil
}

// buildMenuImagePrompt creates a prompt for menu image analysis
func (s *OpenAIService) buildMenuImagePrompt(cuisineType, priceRange, restaurantName string) string {
	return fmt.Sprintf(`Analyze this menu image and extract all menu items. Return the result as a JSON array of menu items.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s

For each menu item, provide:
- name: The dish name
- description: A detailed, appetizing description (2-3 sentences)
- price: Estimated price as a number (consider the price range)
- category: Food category (e.g., "Appetizers", "Main Course", "Desserts")
- ingredients: Array of main ingredients
- allergens: Array of common allergens if applicable
- is_vegetarian: boolean
- is_vegan: boolean
- is_gluten_free: boolean
- is_spicy: boolean
- spice_level: number 0-5 (0 = not spicy)
- preparation_time: estimated minutes
- tags: Array of relevant tags

Return ONLY a valid JSON array, no additional text or formatting.`, restaurantName, cuisineType, priceRange)
}

// buildFoodImagePrompt creates a prompt for food image analysis
func (s *OpenAIService) buildFoodImagePrompt(cuisineType, priceRange, restaurantName string, imageNumber int) string {
	return fmt.Sprintf(`Analyze this food image and create a menu item for it. Return the result as a JSON array with one menu item.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s
- Image #%d

Create an appealing menu item based on what you see in the image. Include:
- name: Creative, appetizing dish name
- description: Detailed, mouth-watering description (2-3 sentences)
- price: Appropriate price as a number (consider the price range)
- category: Food category based on the dish type
- ingredients: Array of visible/likely ingredients
- allergens: Array of potential allergens
- is_vegetarian: boolean based on visible ingredients
- is_vegan: boolean based on visible ingredients
- is_gluten_free: boolean assessment
- is_spicy: boolean based on visual cues
- spice_level: number 0-5 if spicy
- preparation_time: estimated cooking time in minutes
- tags: Array of descriptive tags

Return ONLY a valid JSON array with one object, no additional text.`, restaurantName, cuisineType, priceRange, imageNumber)
}

// buildTextEnhancementPrompt creates a prompt for text enhancement
func (s *OpenAIService) buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName string) string {
	return fmt.Sprintf(`Parse and enhance this menu text into structured menu items. Return as a JSON array.

Restaurant Context:
- Name: %s
- Cuisine Type: %s
- Price Range: %s

Menu Text:
%s

For each item found in the text, create a structured menu item with:
- name: Clean dish name
- description: Enhanced, appetizing description (2-3 sentences)
- price: Extract or estimate price as number (use price range as guide)
- category: Determine appropriate category
- ingredients: Infer likely ingredients
- allergens: Common allergens for this type of dish
- is_vegetarian: boolean assessment
- is_vegan: boolean assessment
- is_gluten_free: boolean assessment
- is_spicy: boolean based on dish name/description
- spice_level: number 0-5 if spicy
- preparation_time: estimated minutes
- tags: Relevant descriptive tags

Return ONLY a valid JSON array, no additional text.`, restaurantName, cuisineType, priceRange, menuText)
}

// parseMenuItemsFromJSON parses the OpenAI response into menu items
func (s *OpenAIService) parseMenuItemsFromJSON(content string) ([]models.AIGeneratedMenuItem, error) {
	// Clean the content - remove any markdown formatting
	content = strings.TrimSpace(content)
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")
	content = strings.TrimSpace(content)

	var menuItems []models.AIGeneratedMenuItem
	if err := json.Unmarshal([]byte(content), &menuItems); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	// Validate and set defaults for required fields
	for i := range menuItems {
		if menuItems[i].Name == "" {
			menuItems[i].Name = fmt.Sprintf("Menu Item %d", i+1)
		}
		if menuItems[i].Description == "" {
			menuItems[i].Description = "Delicious dish prepared with care"
		}
		if menuItems[i].Price <= 0 {
			menuItems[i].Price = 12.99 // Default price
		}
		if menuItems[i].Category == "" {
			menuItems[i].Category = "Main Course"
		}
		if menuItems[i].PreparationTime <= 0 {
			menuItems[i].PreparationTime = 15 // Default prep time
		}
		if len(menuItems[i].Ingredients) == 0 {
			menuItems[i].Ingredients = []string{"fresh ingredients"}
		}
		if len(menuItems[i].Tags) == 0 {
			menuItems[i].Tags = []string{"fresh", "homemade"}
		}
	}

	return menuItems, nil
}
