package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Upload   UploadConfig   `mapstructure:"upload"`
	GCS      GCSConfig      `mapstructure:"gcs"`
	SMTP     SMTPConfig     `mapstructure:"smtp"`
	QRCode   QRCodeConfig   `mapstructure:"qrcode"`
	Rate     RateConfig     `mapstructure:"rate"`
	Log      LogConfig      `mapstructure:"log"`
	CORS     CORSConfig     `mapstructure:"cors"`
	Metrics  MetricsConfig  `mapstructure:"metrics"`
	Security SecurityConfig `mapstructure:"security"`
	OpenAI   OpenAIConfig   `mapstructure:"openai"`
}

type ServerConfig struct {
	Port    string `mapstructure:"port"`
	Env     string `mapstructure:"env"`
	GinMode string `mapstructure:"gin_mode"`
}

type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Name     string `mapstructure:"name"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	SSLMode  string `mapstructure:"ssl_mode"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type JWTConfig struct {
	Secret    string        `mapstructure:"secret"`
	ExpiresIn time.Duration `mapstructure:"expires_in"`
	Issuer    string        `mapstructure:"issuer"`
	Audience  string        `mapstructure:"audience"`
}

type UploadConfig struct {
	Path             string   `mapstructure:"path"`
	MaxSize          int64    `mapstructure:"max_size"`
	AllowedFileTypes []string `mapstructure:"allowed_file_types"`
}

type GCSConfig struct {
	ProjectID      string `mapstructure:"project_id"`
	BucketName     string `mapstructure:"bucket_name"`
	CredentialsKey string `mapstructure:"credentials_key"`
	BaseURL        string `mapstructure:"base_url"`
}

type SMTPConfig struct {
	Host      string `mapstructure:"host"`
	Port      int    `mapstructure:"port"`
	Username  string `mapstructure:"username"`
	Password  string `mapstructure:"password"`
	FromEmail string `mapstructure:"from_email"`
}

type QRCodeConfig struct {
	BaseURL string `mapstructure:"base_url"`
	Size    string `mapstructure:"size"`
}

type RateConfig struct {
	Requests int           `mapstructure:"requests"`
	Window   time.Duration `mapstructure:"window"`
}

type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

type CORSConfig struct {
	AllowedOrigins []string `mapstructure:"allowed_origins"`
	AllowedMethods []string `mapstructure:"allowed_methods"`
	AllowedHeaders []string `mapstructure:"allowed_headers"`
}

type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Path    string `mapstructure:"path"`
}

type SecurityConfig struct {
	BcryptCost       int           `mapstructure:"bcrypt_cost"`
	SessionTimeout   time.Duration `mapstructure:"session_timeout"`
	MaxLoginAttempts int           `mapstructure:"max_login_attempts"`
	LockoutDuration  time.Duration `mapstructure:"lockout_duration"`
}

type OpenAIConfig struct {
	APIKey      string  `mapstructure:"api_key"`
	Model       string  `mapstructure:"model"`
	MaxTokens   int     `mapstructure:"max_tokens"`
	Temperature float32 `mapstructure:"temperature"`
}

// LoadConfig loads configuration from environment variables and config files
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set default values
	setDefaults()

	// Bind environment variables
	bindEnvVars()

	// Read config file if it exists
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return &config, nil
}

func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.env", "development")
	viper.SetDefault("server.gin_mode", "debug")

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", "5432")
	viper.SetDefault("database.ssl_mode", "disable")

	// Redis defaults (local development)
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", "6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	// JWT defaults
	viper.SetDefault("jwt.expires_in", "24h")
	viper.SetDefault("jwt.issuer", "restaurant-api")
	viper.SetDefault("jwt.audience", "restaurant-app")

	// Upload defaults
	viper.SetDefault("upload.path", "./uploads")
	viper.SetDefault("upload.max_size", 10485760) // 10MB
	viper.SetDefault("upload.allowed_file_types", []string{"jpg", "jpeg", "png", "gif", "pdf"})

	// QR Code defaults
	viper.SetDefault("qrcode.base_url", "https://api.qrserver.com/v1/create-qr-code/")
	viper.SetDefault("qrcode.size", "200x200")

	// Rate limiting defaults
	viper.SetDefault("rate.requests", 100)
	viper.SetDefault("rate.window", "1m")

	// Logging defaults
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")

	// CORS defaults
	viper.SetDefault("cors.allowed_origins", []string{"*"})
	viper.SetDefault("cors.allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"})
	viper.SetDefault("cors.allowed_headers", []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"})

	// Metrics defaults
	viper.SetDefault("metrics.enabled", true)
	viper.SetDefault("metrics.path", "/metrics")

	// Security defaults
	viper.SetDefault("security.bcrypt_cost", 12)
	viper.SetDefault("security.session_timeout", "30m")
	viper.SetDefault("security.max_login_attempts", 5)
	viper.SetDefault("security.lockout_duration", "15m")

	// OpenAI defaults
	viper.SetDefault("openai.model", "gpt-4-vision-preview")
	viper.SetDefault("openai.max_tokens", 4096)
	viper.SetDefault("openai.temperature", 0.7)
}

func bindEnvVars() {
	// Server
	viper.BindEnv("server.port", "PORT")
	viper.BindEnv("server.env", "ENV")
	viper.BindEnv("server.gin_mode", "GIN_MODE")

	// Database
	viper.BindEnv("database.host", "DB_HOST")
	viper.BindEnv("database.port", "DB_PORT")
	viper.BindEnv("database.name", "DB_NAME")
	viper.BindEnv("database.user", "DB_USER")
	viper.BindEnv("database.password", "DB_PASSWORD")
	viper.BindEnv("database.ssl_mode", "DB_SSLMODE")
	viper.BindEnv("database.ssl_mode", "DB_SSL_MODE")

	// Redis
	viper.BindEnv("redis.host", "REDIS_HOST")
	viper.BindEnv("redis.port", "REDIS_PORT")
	viper.BindEnv("redis.password", "REDIS_PASSWORD")
	viper.BindEnv("redis.db", "REDIS_DB")

	// JWT
	viper.BindEnv("jwt.secret", "JWT_SECRET")
	viper.BindEnv("jwt.expires_in", "JWT_EXPIRES_IN")
	viper.BindEnv("jwt.issuer", "JWT_ISSUER")
	viper.BindEnv("jwt.audience", "JWT_AUDIENCE")

	// Upload
	viper.BindEnv("upload.path", "UPLOAD_PATH")
	viper.BindEnv("upload.max_size", "MAX_UPLOAD_SIZE")
	viper.BindEnv("upload.allowed_file_types", "ALLOWED_FILE_TYPES")

	// GCS
	viper.BindEnv("gcs.project_id", "GOOGLE_CLOUD_PROJECT_ID")
	viper.BindEnv("gcs.bucket_name", "GOOGLE_CLOUD_BUCKET_NAME")
	viper.BindEnv("gcs.credentials_key", "GOOGLE_APPLICATION_CREDENTIALS")
	viper.BindEnv("gcs.base_url", "GCS_BASE_URL")

	// SMTP
	viper.BindEnv("smtp.host", "SMTP_HOST")
	viper.BindEnv("smtp.port", "SMTP_PORT")
	viper.BindEnv("smtp.username", "SMTP_USERNAME")
	viper.BindEnv("smtp.password", "SMTP_PASSWORD")
	viper.BindEnv("smtp.from_email", "FROM_EMAIL")

	// QR Code
	viper.BindEnv("qrcode.base_url", "QR_CODE_BASE_URL")
	viper.BindEnv("qrcode.size", "QR_CODE_SIZE")

	// Rate limiting
	viper.BindEnv("rate.requests", "RATE_LIMIT_REQUESTS")
	viper.BindEnv("rate.window", "RATE_LIMIT_WINDOW")

	// Logging
	viper.BindEnv("log.level", "LOG_LEVEL")
	viper.BindEnv("log.format", "LOG_FORMAT")

	// CORS
	viper.BindEnv("cors.allowed_origins", "CORS_ALLOWED_ORIGINS")
	viper.BindEnv("cors.allowed_methods", "CORS_ALLOWED_METHODS")
	viper.BindEnv("cors.allowed_headers", "CORS_ALLOWED_HEADERS")

	// Metrics
	viper.BindEnv("metrics.enabled", "ENABLE_METRICS")
	viper.BindEnv("metrics.path", "METRICS_PATH")

	// Security
	viper.BindEnv("security.bcrypt_cost", "BCRYPT_COST")
	viper.BindEnv("security.session_timeout", "SESSION_TIMEOUT")
	viper.BindEnv("security.max_login_attempts", "MAX_LOGIN_ATTEMPTS")
	viper.BindEnv("security.lockout_duration", "LOCKOUT_DURATION")

	// OpenAI
	viper.BindEnv("openai.api_key", "OPENAI_API_KEY")
	viper.BindEnv("openai.model", "OPENAI_MODEL")
	viper.BindEnv("openai.max_tokens", "OPENAI_MAX_TOKENS")
	viper.BindEnv("openai.temperature", "OPENAI_TEMPERATURE")
}

// GetDSN returns the database connection string
func (c *Config) GetDSN() string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=UTC",
		c.Database.Host,
		c.Database.User,
		c.Database.Password,
		c.Database.Name,
		c.Database.Port,
		c.Database.SSLMode,
	)
}

// GetRedisAddr returns the Redis connection address
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Redis.Host, c.Redis.Port)
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Server.Env == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Server.Env == "production"
}
