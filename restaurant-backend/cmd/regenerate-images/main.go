package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Utility to regenerate images for existing AI generation jobs
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run cmd/regenerate-images/main.go <job_id>")
		fmt.Println("Example: go run cmd/regenerate-images/main.go f29d0c70-64d7-4ae8-a6d7-97050cd3a3ab")
		os.Exit(1)
	}

	jobIDStr := os.Args[1]
	jobID, err := uuid.Parse(jobIDStr)
	if err != nil {
		log.Fatalf("Invalid job ID: %v", err)
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize OpenAI service
	openaiService, err := services.NewOpenAIService(cfg.OpenAI)
	if err != nil {
		log.Fatalf("Failed to initialize OpenAI service: %v", err)
	}

	// Find the AI generation job
	var job models.AIGenerationJob
	if err := db.Where("id = ?", jobID).First(&job).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Fatalf("AI generation job not found: %s", jobIDStr)
		}
		log.Fatalf("Failed to find job: %v", err)
	}

	fmt.Printf("Found job: %s (Type: %s, Status: %s)\n", job.ID, job.Type, job.Status)
	fmt.Printf("Menu items: %d\n", len(job.OutputData.MenuItems))

	if len(job.OutputData.MenuItems) == 0 {
		fmt.Println("No menu items found in this job")
		return
	}

	// Regenerate images for each menu item
	ctx := context.Background()
	updatedItems := make([]models.AIGeneratedMenuItem, len(job.OutputData.MenuItems))
	copy(updatedItems, job.OutputData.MenuItems)

	fmt.Println("\nRegenerating images...")
	for i := range updatedItems {
		item := &updatedItems[i]
		fmt.Printf("Generating image for: %s\n", item.Name)

		imageURL, err := openaiService.GenerateFoodImage(
			ctx,
			item.Name,
			item.Description,
			job.InputData.CuisineType,
		)
		if err != nil {
			fmt.Printf("  ❌ Failed to generate image: %v\n", err)
			continue
		}

		item.ImageURL = imageURL
		fmt.Printf("  ✅ Generated: %s\n", imageURL)
	}

	// Update the job in database
	job.OutputData.MenuItems = updatedItems
	if err := db.Save(&job).Error; err != nil {
		log.Fatalf("Failed to update job: %v", err)
	}

	fmt.Printf("\n✅ Successfully regenerated images for job %s\n", jobID)
	fmt.Println("You can now refresh the review page to see the new images!")
}
