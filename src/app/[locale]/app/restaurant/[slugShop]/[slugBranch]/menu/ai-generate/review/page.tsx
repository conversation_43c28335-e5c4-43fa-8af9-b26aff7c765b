'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import React from 'react';
import styles from './review.module.css';
import EditItemModal from './components/EditItemModal';
import { useGetAIGenerationJobBySlugQuery, usePublishMenuBySlugMutation } from '@/lib/redux/api/endpoints/restaurant/aiGenerationApi';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { toast } from 'sonner';

interface ReviewMenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function ReviewMenuPage({ params }: ReviewMenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const t = useTranslations('restaurant');
  const router = useRouter();
  const searchParams = useSearchParams();
  const jobId = searchParams.get('jobId');

  // API hooks
  const { data: job, isLoading, error, refetch } = useGetAIGenerationJobBySlugQuery(
    { slugShop, slugBranch, jobId: jobId || '' },
    { skip: !jobId }
  );

  const [publishMenu, { isLoading: isPublishing }] = usePublishMenuBySlugMutation();

  // State for menu items
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const [menuData, setMenuData] = useState<any>(null);

  // Update local state when job data changes
  useEffect(() => {
    if (job?.output_data?.menu_items) {
      setMenuItems(job.output_data.menu_items);
    }
    if (job?.output_data?.menu_info) {
      setMenuData(job.output_data.menu_info);
    }
  }, [job]);

  // Handle publishing menu
  const handlePublishMenu = async () => {
    if (!jobId) {
      toast.error('No job ID found');
      return;
    }

    try {
      await publishMenu({
        slugShop,
        slugBranch,
        jobId,
        menuItems: menuItems.map(item => ({
          name: item.name,
          description: item.description,
          price: item.price,
          category: item.category,
          image_url: item.image_url || item.image,
          dietary_info: item.dietary_info || [],
          ingredients: item.ingredients || [],
          allergens: item.allergens || []
        }))
      }).unwrap();

      toast.success('Menu published successfully!');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu`);
    } catch (error) {
      console.error('Failed to publish menu:', error);
      toast.error('Failed to publish menu. Please try again.');
    }
  };

  // State for edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<any>(null);

  // Handle edit item
  const handleEditItem = (itemId: string) => {
    const itemToEdit = menuItems.find(item => item.id === itemId || menuItems.indexOf(item).toString() === itemId);
    if (itemToEdit) {
      setCurrentEditItem(itemToEdit);
      setIsEditModalOpen(true);
    }
  };

  // Handle save edited item
  const handleSaveEditedItem = (editedItem: any) => {
    setMenuItems(menuItems.map(item =>
      (item.id === editedItem.id || menuItems.indexOf(item) === menuItems.indexOf(editedItem)) ? editedItem : item
    ));
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#e58219]" />
          <p className="text-[#887663]">Loading AI generation results...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !job) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4 text-center">
          <AlertCircle className="h-8 w-8 text-red-500" />
          <div>
            <p className="text-[#181511] font-semibold">Failed to load AI generation results</p>
            <p className="text-[#887663] text-sm mt-1">
              {error ? 'An error occurred while loading the data.' : 'Job not found.'}
            </p>
          </div>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-[#e58219] text-[#181511] rounded-lg font-medium hover:bg-[#d4751a] transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Job not completed
  if (job.status !== 'completed') {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4 text-center">
          {job.status === 'processing' && <Loader2 className="h-8 w-8 animate-spin text-[#e58219]" />}
          {job.status === 'failed' && <AlertCircle className="h-8 w-8 text-red-500" />}
          <div>
            <p className="text-[#181511] font-semibold">
              {job.status === 'processing' && 'AI generation in progress...'}
              {job.status === 'failed' && 'AI generation failed'}
              {job.status === 'pending' && 'AI generation pending...'}
            </p>
            <p className="text-[#887663] text-sm mt-1">
              {job.status === 'processing' && `Progress: ${job.progress}%`}
              {job.status === 'failed' && job.error_msg}
              {job.status === 'pending' && 'Your request is in the queue.'}
            </p>
          </div>
          {job.status === 'processing' && (
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-[#e58219] text-[#181511] rounded-lg font-medium hover:bg-[#d4751a] transition-colors"
            >
              Refresh
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">Review and Edit Menu</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">
            {job.type === 'menu_image' && 'Generated from uploaded menu image.'}
            {job.type === 'food_images' && 'Generated from uploaded food images.'}
            {job.type === 'text' && 'Generated from menu text with AI-created images.'}
            {' '}Please review and finalize before publishing.
          </p>
          {job.type === 'text' && (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              <p className="text-green-800 text-sm font-medium">
                Custom food images generated with DALL-E 3
              </p>
            </div>
          )}
        </div>
      </div>

      {menuData && (
        <>
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Menu Preview</h3>
          <div className="p-4 @container">
            <div className="flex flex-col items-stretch justify-start rounded-xl @xl:flex-row @xl:items-start">
              {menuData.image && (
                <div
                  className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                  style={{backgroundImage: `url("${menuData.image}")`}}
                ></div>
              )}
              <div className="flex w-full min-w-72 grow flex-col items-stretch justify-center gap-1 py-4 @xl:px-4">
                <p className="text-[#887663] text-sm font-normal leading-normal">AI Generated Menu</p>
                <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">
                  {menuData.name || 'AI Generated Menu'}
                </p>
                <div className="flex items-end gap-3 justify-between">
                  <p className="text-[#887663] text-base font-normal leading-normal">
                    {menuData.description || 'Menu items generated using AI technology'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">
        Menu Items ({menuItems.length})
      </h3>

      {menuItems.length === 0 ? (
        <div className="px-4 py-8 text-center">
          <p className="text-[#887663]">No menu items generated yet.</p>
        </div>
      ) : (
        <div className="px-4 py-3 @container">
          <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
            <table className="flex-1">
              <thead>
                <tr className="bg-white">
                  <th className="table-column-120 px-4 py-3 text-left text-[#181511] w-[120px] text-sm font-medium leading-normal">Image</th>
                  <th className="table-column-240 px-4 py-3 text-left text-[#181511] w-[200px] text-sm font-medium leading-normal">Item</th>
                  <th className="table-column-360 px-4 py-3 text-left text-[#181511] w-[300px] text-sm font-medium leading-normal">
                    Description
                  </th>
                  <th className="table-column-480 px-4 py-3 text-left text-[#181511] w-[100px] text-sm font-medium leading-normal">Price</th>
                  <th className="table-column-600 px-4 py-3 text-left w-60 text-[#887663] text-sm font-medium leading-normal">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {menuItems.map((item, index) => (
                  <tr key={item.id || index} className="border-t border-t-[#e5e1dc]">
                    <td className="table-column-120 h-[72px] px-4 py-2 w-[120px]">
                      {(item.image_url || item.image) ? (
                        <img
                          src={item.image_url || item.image}
                          alt={item.name}
                          className="w-12 h-12 object-cover rounded-lg"
                          onError={(e) => {
                            e.currentTarget.src = '/images/placeholder-food.jpg';
                          }}
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </td>
                    <td className="table-column-240 h-[72px] px-4 py-2 w-[200px] text-[#181511] text-sm font-normal leading-normal">
                      <div>
                        <p className="font-medium">{item.name}</p>
                        {item.category && (
                          <p className="text-xs text-[#887663]">{item.category}</p>
                        )}
                      </div>
                    </td>
                    <td className="table-column-360 h-[72px] px-4 py-2 w-[300px] text-[#887663] text-sm font-normal leading-normal">
                      <p className="line-clamp-2">{item.description}</p>
                    </td>
                    <td className="table-column-480 h-[72px] px-4 py-2 w-[100px] text-[#887663] text-sm font-normal leading-normal">
                      ${typeof item.price === 'number' ? item.price.toFixed(2) : parseFloat(item.price || '0').toFixed(2)}
                    </td>
                    <td className="table-column-600 h-[72px] px-4 py-2 w-60 text-[#887663] text-sm font-bold leading-normal tracking-[0.015em] cursor-pointer" onClick={() => handleEditItem(item.id || index.toString())}>
                      Edit
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <style dangerouslySetInnerHTML={{
            __html: `
              @container(max-width:120px){.table-column-120{display: none;}}
              @container(max-width:240px){.table-column-240{display: none;}}
              @container(max-width:360px){.table-column-360{display: none;}}
              @container(max-width:480px){.table-column-480{display: none;}}
              @container(max-width:600px){.table-column-600{display: none;}}
            `
          }} />
        </div>
      )}

      <div className="flex px-4 py-3 justify-between items-center">
        <Link
          href={`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate`}
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 border border-[#e5e1dc] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-gray-50 transition-colors"
        >
          <span className="truncate">Back to AI Generate</span>
        </Link>

        <button
          onClick={handlePublishMenu}
          disabled={isPublishing || menuItems.length === 0}
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#d4751a] transition-colors"
        >
          {isPublishing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="truncate">Publishing...</span>
            </>
          ) : (
            <span className="truncate">Publish Menu ({menuItems.length} items)</span>
          )}
        </button>
      </div>

      {/* Edit Item Modal */}
      <EditItemModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        item={currentEditItem}
        onSave={handleSaveEditedItem}
      />
    </>
  );
}
