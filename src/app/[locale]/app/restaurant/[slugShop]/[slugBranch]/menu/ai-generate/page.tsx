'use client';

import { useState, useRef, ChangeEvent } from 'react';
import { useRouter } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import {
  useCreateAIGenerationJobMutation,
  useUploadAIFileMutation,
  AIGenerationJob
} from '@/lib/redux/api/endpoints/restaurant/aiGenerationApi';
import React from 'react';

interface AIGenerateMenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function AIGenerateMenuPage({ params }: AIGenerateMenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const router = useRouter();

  // Use the slug values for API calls
  const shopSlug = slugShop;
  const branchSlug = slugBranch;

  // API hooks
  const [createAIGenerationJob] = useCreateAIGenerationJobMutation();
  const [uploadAIFile] = useUploadAIFileMutation();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'menu_image' | 'text' | 'food_images'>('menu_image');

  // State for file uploads
  const [menuImage, setMenuImage] = useState<File | null>(null);
  const [menuImagePreview, setMenuImagePreview] = useState<string | null>(null);
  const [foodImages, setFoodImages] = useState<File[]>([]);
  const [foodImagesPreview, setFoodImagesPreview] = useState<string[]>([]);

  // State for text input
  const [menuText, setMenuText] = useState('');

  // State for additional context
  const [cuisineType, setCuisineType] = useState('');
  const [priceRange, setPriceRange] = useState('$$');
  const [restaurantName, setRestaurantName] = useState('');

  // State for generation process
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJob, setCurrentJob] = useState<AIGenerationJob | null>(null);

  // Refs for file inputs
  const menuImageInputRef = useRef<HTMLInputElement>(null);
  const foodImagesInputRef = useRef<HTMLInputElement>(null);

  // Handle menu image upload
  const handleMenuImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setMenuImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = () => {
        setMenuImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle food images upload
  const handleFoodImagesUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      setFoodImages(prevImages => [...prevImages, ...files]);

      // Create previews
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = () => {
          setFoodImagesPreview(prev => [...prev, reader.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Handle text input
  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setMenuText(e.target.value);
  };

  // Handle form submission for menu image
  const handleGenerateFromMenuImage = async () => {
    if (!menuImage) return;

    setIsGenerating(true);

    try {
      // First upload the image
      const uploadResult = await uploadAIFile({
        shopSlug,
        branchSlug,
        file: menuImage,
        type: 'menu_image'
      }).unwrap();

      // Then create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'menu_image',
          input_data: {
            menu_image_url: uploadResult.file_url,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from image:', error);
      setIsGenerating(false);
    }
  };

  // Handle form submission for text
  const handleGenerateFromText = async () => {
    if (!menuText.trim()) return;

    setIsGenerating(true);

    try {
      // Create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'text',
          input_data: {
            menu_text: menuText,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from text:', error);
      setIsGenerating(false);
    }
  };

  // Handle form submission for food images
  const handleGenerateFromFoodImages = async () => {
    if (foodImages.length === 0) return;

    setIsGenerating(true);

    try {
      // Upload all food images
      const uploadPromises = foodImages.map(file =>
        uploadAIFile({
          shopSlug,
          branchSlug,
          file,
          type: 'food_image'
        }).unwrap()
      );

      const uploadResults = await Promise.all(uploadPromises);
      const imageUrls = uploadResults.map(result => result.file_url);

      // Create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'food_images',
          input_data: {
            food_image_urls: imageUrls,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from food images:', error);
      setIsGenerating(false);
    }
  };



  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-foreground tracking-light text-[32px] font-bold leading-tight">AI Menu Generator</p>
          <p className="text-muted-foreground text-sm font-normal leading-normal">Generate menu items using AI based on your restaurant's menu images, text, or food photos.</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="pb-3">
        <div className="flex border-b border-[#e5e1dc] px-4 gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'menu_image' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('menu_image'); }}
          >
            <p className={`${activeTab === 'menu_image' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Menu Image</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'text' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('text'); }}
          >
            <p className={`${activeTab === 'text' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Text</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'food_images' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('food_images'); }}
          >
            <p className={`${activeTab === 'food_images' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Food Images</p>
          </a>
        </div>
      </div>

      {/* Menu Image Tab */}
      {activeTab === 'menu_image' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-[#887663] text-sm font-normal leading-normal mb-4">
              Upload a clear image of your menu, and our AI will automatically create an interactive online menu for your restaurant.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e5e1dc] px-6 py-14">
              {menuImagePreview ? (
                <div className="flex flex-col items-center gap-4 w-full">
                  <img
                    src={menuImagePreview}
                    alt="Menu preview"
                    className="max-w-full max-h-[300px] object-contain rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setMenuImage(null);
                      setMenuImagePreview(null);
                    }}
                    className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
                  >
                    <span className="truncate">Remove</span>
                  </button>
                </div>
              ) : (
                <>
                  <div className="flex max-w-[480px] flex-col items-center gap-2">
                    <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Drag and drop or browse to upload</p>
                    <p className="text-[#887663] text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG, WebP. Max size: 5MB</p>
                  </div>
                  <input
                    type="file"
                    ref={menuImageInputRef}
                    onChange={handleMenuImageUpload}
                    accept="image/jpeg,image/png,image/webp"
                    className="hidden"
                  />
                  <button
                    onClick={() => menuImageInputRef.current?.click()}
                    className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
                  >
                    <span className="truncate">Browse Files</span>
                  </button>
                </>
              )}
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <button
              onClick={handleGenerateFromMenuImage}
              disabled={!menuImage || isGenerating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </button>
          </div>
        </>
      )}

      {/* Text Tab */}
      {activeTab === 'text' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-muted-foreground text-sm font-normal leading-normal mb-4">
              Enter your menu items as text, and our AI will format them into a structured menu with descriptions and prices.
            </p>

            <div className="flex flex-col gap-4">
              <textarea
                value={menuText}
                onChange={handleTextChange}
                placeholder="Enter your menu items here... For example:&#10;&#10;APPETIZERS&#10;Garlic Bread - $5.99&#10;Mozzarella Sticks - $7.99&#10;&#10;MAIN COURSES&#10;Spaghetti Bolognese - $14.99&#10;Grilled Salmon - $18.99"
                className="w-full min-h-[300px] p-4 border border-border rounded-xl resize-none focus:outline-none focus:ring-1 focus:ring-ring text-foreground"
              />
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <Button
              onClick={handleGenerateFromText}
              disabled={!menuText.trim() || isGenerating}
              className="min-w-[84px] max-w-[480px]"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </Button>
          </div>
        </>
      )}

      {/* Food Images Tab */}
      {activeTab === 'food_images' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-[#887663] text-sm font-normal leading-normal mb-4">
              Upload photos of your dishes, and our AI will create menu items with descriptions and suggested prices.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e5e1dc] px-6 py-14">
              <div className="flex max-w-[480px] flex-col items-center gap-2">
                <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Drag and drop or browse to upload</p>
                <p className="text-[#887663] text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG, WebP. Max size: 5MB per image</p>
              </div>
              <input
                type="file"
                ref={foodImagesInputRef}
                onChange={handleFoodImagesUpload}
                accept="image/jpeg,image/png,image/webp"
                multiple
                className="hidden"
              />
              <button
                onClick={() => foodImagesInputRef.current?.click()}
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span className="truncate">Browse Files</span>
              </button>
            </div>

            {foodImagesPreview.length > 0 && (
              <div className="mt-6">
                <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] mb-4">Uploaded Images</p>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {foodImagesPreview.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Food image ${index + 1}`}
                        className="w-full h-40 object-cover rounded-lg"
                      />
                      <button
                        onClick={() => {
                          setFoodImages(prev => prev.filter((_, i) => i !== index));
                          setFoodImagesPreview(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#181511" viewBox="0 0 256 256">
                          <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"></path>
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="flex px-4 py-3 justify-end">
            <button
              onClick={handleGenerateFromFoodImages}
              disabled={foodImages.length === 0 || isGenerating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </button>
          </div>
        </>
      )}


    </>
  );
}
