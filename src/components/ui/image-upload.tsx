'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, Camera, RotateCcw, FileImage } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

export interface ImageUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove?: () => void;
  onReset?: () => void;
  currentImageUrl?: string;
  previewUrl?: string;
  isUploading?: boolean;
  disabled?: boolean;
  className?: string;
  
  // Validation options
  maxSizeBytes?: number;
  allowedTypes?: string[];
  
  // UI customization
  title?: string;
  description?: string;
  dragDropText?: string;
  supportedFormatsText?: string;
  showRemoveButton?: boolean;
  showResetButton?: boolean;
  resetButtonText?: string;
  
  // Image display options
  imageClassName?: string;
  imageContainerClassName?: string;
  aspectRatio?: 'square' | 'video' | 'auto';
}

const DEFAULT_ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const DEFAULT_MAX_SIZE = 5 * 1024 * 1024; // 5MB

export function ImageUpload({
  onFileSelect,
  onFileRemove,
  onReset,
  currentImageUrl,
  previewUrl,
  isUploading = false,
  disabled = false,
  className,
  
  // Validation
  maxSizeBytes = DEFAULT_MAX_SIZE,
  allowedTypes = DEFAULT_ALLOWED_TYPES,
  
  // UI customization
  title = "Upload Image",
  description,
  dragDropText = "Drag and drop an image here, or click to browse",
  supportedFormatsText,
  showRemoveButton = true,
  showResetButton = false,
  resetButtonText = "Reset",
  
  // Image display
  imageClassName,
  imageContainerClassName,
  aspectRatio = 'auto'
}: ImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate supported formats text if not provided
  const defaultSupportedFormatsText = React.useMemo(() => {
    const formats = allowedTypes.map(type => {
      switch (type) {
        case 'image/jpeg': return 'JPG';
        case 'image/png': return 'PNG';
        case 'image/webp': return 'WebP';
        case 'image/gif': return 'GIF';
        case 'image/svg+xml': return 'SVG';
        default: return type.replace('image/', '').toUpperCase();
      }
    }).join(', ');
    
    const sizeText = maxSizeBytes ? ` Max size: ${Math.round(maxSizeBytes / (1024 * 1024))}MB` : '';
    return `Supported formats: ${formats}.${sizeText}`;
  }, [allowedTypes, maxSizeBytes]);

  const finalSupportedFormatsText = supportedFormatsText || defaultSupportedFormatsText;

  const validateFile = useCallback((file: File): boolean => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      const formats = allowedTypes.map(type => type.replace('image/', '').toUpperCase()).join(', ');
      toast.error(`Please select a ${formats} image file`);
      return false;
    }

    // Check file size
    if (maxSizeBytes && file.size > maxSizeBytes) {
      const maxSizeMB = Math.round(maxSizeBytes / (1024 * 1024));
      toast.error(`File size must be less than ${maxSizeMB}MB`);
      return false;
    }

    return true;
  }, [allowedTypes, maxSizeBytes]);

  const handleFileSelect = useCallback((file: File) => {
    if (!validateFile(file)) return;
    onFileSelect(file);
  }, [validateFile, onFileSelect]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isUploading) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled || isUploading) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleBrowseClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  const displayImageUrl = previewUrl || currentImageUrl;

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square': return 'aspect-square';
      case 'video': return 'aspect-video';
      default: return 'h-64';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Title */}
      {title && (
        <h3 className="text-[#181510] font-medium">{title}</h3>
      )}
      
      {/* Description */}
      {description && (
        <p className="text-[#8a745c] text-sm">{description}</p>
      )}

      {/* Image Preview */}
      {displayImageUrl && (
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div className={cn("relative w-full", getAspectRatioClass(), imageContainerClassName)}>
              <img
                src={displayImageUrl}
                alt="Preview"
                className={cn("w-full h-full object-cover", imageClassName)}
              />
              
              {/* Loading overlay */}
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                    <p className="text-sm">Uploading...</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Controls */}
      <div className="space-y-3">
        {/* Drag and Drop Area */}
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            isDragOver
              ? 'border-[#e5ccb2] bg-[#f1edea]'
              : 'border-[#e2dcd4] hover:border-[#e5ccb2] hover:bg-[#f9f8f7]',
            (disabled || isUploading) && 'opacity-50 cursor-not-allowed'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Camera className="mx-auto h-8 w-8 text-[#8a745c] mb-2" />
          <p className="text-[#181510] font-medium mb-1">
            {dragDropText}
          </p>
          <p className="text-[#8a745c] text-sm mb-4">
            {finalSupportedFormatsText}
          </p>

          <input
            ref={fileInputRef}
            type="file"
            accept={allowedTypes.join(',')}
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled || isUploading}
          />

          <Button
            variant="outline"
            onClick={handleBrowseClick}
            disabled={disabled || isUploading}
            className="border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
          >
            <Upload className="w-4 h-4 mr-2" />
            Choose File
          </Button>
        </div>

        {/* Action Buttons */}
        {(showRemoveButton || showResetButton) && (
          <div className="flex gap-2">
            {showRemoveButton && onFileRemove && currentImageUrl && (
              <Button
                variant="outline"
                onClick={onFileRemove}
                disabled={disabled || isUploading}
                className="flex-1 border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
              >
                <X className="w-4 h-4 mr-2" />
                Remove
              </Button>
            )}
            
            {showResetButton && onReset && (
              <Button
                variant="outline"
                onClick={onReset}
                disabled={disabled || isUploading}
                className="flex-1 border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                {resetButtonText}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Specialized variants for common use cases
export function MenuItemImageUpload(props: Omit<ImageUploadProps, 'title' | 'aspectRatio'> & { itemName?: string }) {
  return (
    <ImageUpload
      {...props}
      title={props.itemName ? `${props.itemName} Image` : "Menu Item Image"}
      aspectRatio="square"
      allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
    />
  );
}

export function TableImageUpload(props: Omit<ImageUploadProps, 'title' | 'aspectRatio'> & { tableName?: string }) {
  return (
    <ImageUpload
      {...props}
      title={props.tableName ? `${props.tableName} Image` : "Table Image"}
      aspectRatio="video"
      allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
    />
  );
}

export function AvatarImageUpload(props: Omit<ImageUploadProps, 'title' | 'aspectRatio' | 'maxSizeBytes'>) {
  return (
    <ImageUpload
      {...props}
      title="Profile Picture"
      aspectRatio="square"
      maxSizeBytes={2 * 1024 * 1024} // 2MB for avatars
      allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
    />
  );
}
