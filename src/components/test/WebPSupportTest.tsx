'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ImageUpload } from '@/components/ui/image-upload';
import { supportsWebP, convertToWebP, formatFileSize } from '@/lib/utils/image-utils';
import { CheckCircle, XCircle, Upload, Download } from 'lucide-react';

export function WebPSupportTest() {
  const [webpSupported, setWebpSupported] = useState<boolean | null>(null);
  const [testFile, setTestFile] = useState<File | null>(null);
  const [convertedFile, setConvertedFile] = useState<File | null>(null);
  const [conversionResult, setConversionResult] = useState<{
    originalSize: number;
    newSize: number;
    compressionRatio: number;
  } | null>(null);
  const [isConverting, setIsConverting] = useState(false);

  useEffect(() => {
    // Check WebP support on component mount
    supportsWebP().then(setWebpSupported);
  }, []);

  const handleFileSelect = (file: File) => {
    setTestFile(file);
    setConvertedFile(null);
    setConversionResult(null);
  };

  const handleConvertToWebP = async () => {
    if (!testFile) return;

    setIsConverting(true);
    try {
      const result = await convertToWebP(testFile, {
        quality: 0.8,
        maxWidth: 1920,
        maxHeight: 1920
      });

      setConvertedFile(result.file);
      setConversionResult({
        originalSize: result.originalSize,
        newSize: result.newSize,
        compressionRatio: result.compressionRatio
      });
    } catch (error) {
      console.error('Conversion failed:', error);
    } finally {
      setIsConverting(false);
    }
  };

  const downloadFile = (file: File, filename: string) => {
    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            WebP Support Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Browser Support Check */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {webpSupported === null ? (
                <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
              ) : webpSupported ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span className="font-medium">
                Browser WebP Support: {webpSupported === null ? 'Checking...' : webpSupported ? 'Supported' : 'Not Supported'}
              </span>
            </div>
          </div>

          {/* File Upload */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Test Image Upload & Conversion</h3>
            <ImageUpload
              onFileSelect={handleFileSelect}
              allowedTypes={['image/jpeg', 'image/png', 'image/gif']}
              maxSizeBytes={10 * 1024 * 1024} // 10MB for testing
              title="Upload Test Image"
              description="Upload a JPEG, PNG, or GIF image to test WebP conversion"
              supportedFormatsText="Supported: JPG, PNG, GIF. Max size: 10MB"
              currentImageUrl={testFile ? URL.createObjectURL(testFile) : undefined}
              showRemoveButton={false}
            />
          </div>

          {/* Conversion Controls */}
          {testFile && (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <p className="font-medium">Original File</p>
                  <p className="text-sm text-gray-600">
                    {testFile.name} ({formatFileSize(testFile.size)})
                  </p>
                </div>
                <Button
                  onClick={handleConvertToWebP}
                  disabled={isConverting}
                  className="flex items-center gap-2"
                >
                  {isConverting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4" />
                  )}
                  Convert to WebP
                </Button>
              </div>

              {/* Conversion Results */}
              {conversionResult && convertedFile && (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">Conversion Results</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Original Size:</p>
                        <p className="font-medium">{formatFileSize(conversionResult.originalSize)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">WebP Size:</p>
                        <p className="font-medium">{formatFileSize(conversionResult.newSize)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Compression Ratio:</p>
                        <p className="font-medium text-green-600">
                          {conversionResult.compressionRatio.toFixed(1)}% smaller
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">Format:</p>
                        <p className="font-medium">{convertedFile.type}</p>
                      </div>
                    </div>
                  </div>

                  {/* Download Buttons */}
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => downloadFile(testFile, testFile.name)}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Download Original
                    </Button>
                    <Button
                      onClick={() => downloadFile(convertedFile, convertedFile.name)}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Download WebP
                    </Button>
                  </div>

                  {/* Side-by-side comparison */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-medium mb-2">Original ({testFile.type})</h5>
                      <img
                        src={URL.createObjectURL(testFile)}
                        alt="Original"
                        className="w-full h-48 object-cover rounded-lg border"
                      />
                    </div>
                    <div>
                      <h5 className="font-medium mb-2">WebP Converted</h5>
                      <img
                        src={URL.createObjectURL(convertedFile)}
                        alt="WebP Converted"
                        className="w-full h-48 object-cover rounded-lg border"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Information */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">WebP Benefits</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 25-35% smaller file sizes compared to JPEG/PNG</li>
              <li>• Better image quality at smaller file sizes</li>
              <li>• Faster page loading and reduced bandwidth usage</li>
              <li>• Supported by all modern browsers</li>
              <li>• Automatic fallback for older browsers</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
