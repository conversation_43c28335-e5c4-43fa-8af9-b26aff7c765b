import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base query using Next.js API routes (proxy to backend)
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: async (headers, { endpoint, type, arg, extra }) => {
    // Don't set content-type header at all - let the browser handle it
    // This allows FormData requests to get the correct multipart/form-data content type
    // and JSON requests will get application/json from the body serialization

    // NextAuth session will be handled server-side in API routes
    // No need to manually add auth headers here

    return headers;
  },
});

// Create the API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: ['Merchants', 'Items', 'MenuItems', 'MenuCategories', 'Orders', 'Users', 'Settings', 'Communications', 'Appointments', 'Campaigns', 'CampaignSegments', 'CommunicationAnalytics', 'Reviews', 'ReviewStats', 'Reservations', 'Tables', 'TableAreas', 'TableLayout', 'Shops', 'Shop', 'Branches', 'Floor', 'PurchaseOrders', 'Suppliers', 'Staff', 'StaffRoles', 'Permissions', 'Notifications', 'Reports', 'AIGenerationJob'],
  endpoints: () => ({}),
});
